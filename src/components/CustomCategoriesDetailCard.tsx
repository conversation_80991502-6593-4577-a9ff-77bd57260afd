import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
  Alert,
  Platform,
  TouchableNativeFeedback,
} from 'react-native';
import {Images} from '../assets';
import {useAppNavigation} from '../hooks/useAppNavigation';
import {createImageErrorHandler, getSafeImageSource} from '../utils/imageUtils';
import {UICompany} from '../types/company';
import {useAppShortcuts} from '../hooks/useAppShortcuts';
import {useBookmarkState} from '../hooks/useBookmarkState';
import bookmarkStorageService from '../services/bookmarkStorageService';
import WebSocketVoteService from '../socket/iccappush.js';
import numberRepository from '../database/watermelon/repositories/numberRepository';

interface Props {
  companyData?: UICompany; // Use standardized UICompany interface
}

const CustomCategoriesDetailCard = ({companyData}: Props) => {
  const navigation = useAppNavigation();

  // Initialize app shortcuts hook
  const {isShortcutSupported, createCompanyShortcut, removeCompanyShortcut} =
    useAppShortcuts(navigation);

  // Initialize bookmark state hook
  const {
    isBookmarked,
    hasShortcut,
    toggleBookmark,
    refreshState: refreshBookmarkState,
  } = useBookmarkState(companyData?.companyId, companyData?.companyName);

  // State for image loading fallback
  const [imageSource, setImageSource] = useState(() => {
    return getSafeImageSource(companyData?.companyLogoUrl, Images.defaultIcon);
  });

  // State for voting functionality
  const [isVoting, setIsVoting] = useState(false);
  const [localUpvoteCount, setLocalUpvoteCount] = useState(0);
  const [localDownvoteCount, setLocalDownvoteCount] = useState(0);
  const [numberData, setNumberData] = useState<{
    numberId?: number;
    upvoteCount: number;
    downvoteCount: number;
  } | null>(null);

  // Handle image loading error using utility function
  const handleImageError = createImageErrorHandler(
    'CustomCategoriesDetailCard',
    companyData?.companyLogoUrl,
    {
      companyName: companyData?.companyName,
      companyId: companyData?.companyId,
    },
    () => setImageSource(Images.defaultIcon),
  );

  // Update image source when companyData changes
  useEffect(() => {
    setImageSource(
      getSafeImageSource(companyData?.companyLogoUrl, Images.defaultIcon),
    );
  }, [companyData?.companyLogoUrl]);

  // Fetch number data from database to get vote counts
  // NOTE: This shows vote counts for the SPECIFIC phone number displayed in the card
  // (the first available number). Different numbers can have different vote counts,
  // so this may differ from vote counts shown in the details page for other numbers.
  useEffect(() => {
    const fetchNumberData = async () => {
      if (!companyData?.companyId) {
        return;
      }

      try {
        // Get all numbers for this company to calculate aggregated vote counts
        const numbers = await numberRepository.getByCompanyId(
          companyData.companyId,
        );

        if (numbers.length > 0) {
          // Find the specific number being displayed (first available number)
          const displayedNumber = numbers.find(
            num => num.number === companyData.number,
          );

          if (displayedNumber) {
            const numberInfo = {
              numberId: displayedNumber.api_number_id,
              upvoteCount: displayedNumber.upvote_count || 0,
              downvoteCount: displayedNumber.downvote_count || 0,
            };
            setNumberData(numberInfo);
            setLocalUpvoteCount(numberInfo.upvoteCount);
            setLocalDownvoteCount(numberInfo.downvoteCount);
          } else {
            // If displayed number not found, use company-level vote counts
            setNumberData({
              upvoteCount: companyData.upVoteCount || 0,
              downvoteCount: companyData.downVoteCount || 0,
            });
            setLocalUpvoteCount(companyData.upVoteCount || 0);
            setLocalDownvoteCount(companyData.downVoteCount || 0);
          }
        } else {
          // If no numbers found in database, use company-level vote counts as fallback
          setNumberData({
            upvoteCount: companyData.upVoteCount || 0,
            downvoteCount: companyData.downVoteCount || 0,
          });
          setLocalUpvoteCount(companyData.upVoteCount || 0);
          setLocalDownvoteCount(companyData.downVoteCount || 0);
        }
      } catch (error) {
        // Fallback to company-level vote counts
        setNumberData({
          upvoteCount: companyData.upVoteCount || 0,
          downvoteCount: companyData.downVoteCount || 0,
        });
        setLocalUpvoteCount(companyData.upVoteCount || 0);
        setLocalDownvoteCount(companyData.downVoteCount || 0);
      }
    };

    fetchNumberData();
  }, [
    companyData?.companyId,
    companyData?.number,
    companyData?.upVoteCount,
    companyData?.downVoteCount,
  ]);

  const handlePhoneCall = (number: string | number) => {
    console.log('handlePhoneCall called with:', number, typeof number);

    // Convert to string for processing
    const numberStr = String(number).trim();

    // Check if it's a website URL first
    if (numberStr.startsWith('http')) {
      console.log('Opening website URL:', numberStr);
      Linking.openURL(numberStr).catch((err: Error) =>
        console.error('Failed to open website:', err),
      );
      return;
    }

    // Check if it contains digits (phone numbers can have formatting)
    const hasDigits = /\d/.test(numberStr);

    if (hasDigits && numberStr.length > 0) {
      // Clean the number by removing common formatting characters but keep + for international numbers
      const cleanedNumber = numberStr.replace(/[^\d+]/g, '');
      const phoneNumber = `tel:${cleanedNumber}`;

      console.log('Attempting to call:', phoneNumber);
      Linking.openURL(phoneNumber).catch((err: Error) => {
        console.error('Failed to open phone call:', err);
        // Fallback: try with original number
        const fallbackNumber = `tel:${numberStr}`;
        console.log('Fallback attempt:', fallbackNumber);
        Linking.openURL(fallbackNumber).catch((fallbackErr: Error) =>
          console.error('Fallback phone call also failed:', fallbackErr),
        );
      });
    } else {
      console.log('Invalid phone number format:', numberStr);
    }
  };

  const handleUpVote = async () => {
    if (isVoting || !companyData?.number) return;

    setIsVoting(true);
    try {
      const result = await WebSocketVoteService.sendVote(
        companyData.number,
        true,
      );

      if (result.success) {
        // Update local state immediately
        setLocalUpvoteCount(prev => prev + 1);

        // Update database if we have numberId
        if (numberData?.numberId) {
          try {
            await numberRepository.updateVoteCounts(
              numberData.numberId,
              localUpvoteCount + 1,
              localDownvoteCount,
            );
          } catch (dbError) {
            console.error('Failed to update database:', dbError);
          }
        } else {
          // If no numberId, try to update by phone number
          try {
            await numberRepository.updateVoteCountsByNumber(
              companyData.number,
              localUpvoteCount + 1,
              localDownvoteCount,
            );
          } catch (dbError) {
            console.error(
              'Failed to update database by phone number:',
              dbError,
            );
          }
        }
      } else {
        // Still update local count even if WebSocket failed
        setLocalUpvoteCount(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error during upvote:', error);
      Alert.alert('Error', 'Failed to submit vote. Please try again.');
    } finally {
      setIsVoting(false);
    }
  };

  const handleDownVote = async () => {
    if (isVoting || !companyData?.number) return;

    setIsVoting(true);
    try {
      const result = await WebSocketVoteService.sendVote(
        companyData.number,
        false,
      );

      if (result.success) {
        // Update local state immediately
        setLocalDownvoteCount(prev => prev + 1);

        // Update database if we have numberId
        if (numberData?.numberId) {
          try {
            await numberRepository.updateVoteCounts(
              numberData.numberId,
              localUpvoteCount,
              localDownvoteCount + 1,
            );
          } catch (dbError) {
            console.error('Failed to update database:', dbError);
          }
        } else {
          // If no numberId, try to update by phone number
          try {
            await numberRepository.updateVoteCountsByNumber(
              companyData.number,
              localUpvoteCount,
              localDownvoteCount + 1,
            );
          } catch (dbError) {
            console.error(
              'Failed to update database by phone number:',
              dbError,
            );
          }
        }
      } else {
        // Still update local count even if WebSocket failed
        setLocalDownvoteCount(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error during downvote:', error);
      Alert.alert('Error', 'Failed to submit vote. Please try again.');
    } finally {
      setIsVoting(false);
    }
  };

  const handleBookmark = async () => {
    const id = companyData?.companyId;
    const name = companyData?.companyName;

    if (!id || !name) {
      Alert.alert('Error', 'Company information is not available');
      return;
    }

    try {
      if (isBookmarked) {
        // Remove bookmark and shortcut
        if (hasShortcut && isShortcutSupported) {
          await removeCompanyShortcut(id);
        }
        await toggleBookmark();

        Alert.alert(
          'Bookmark Removed',
          `You removed ${name} from bookmarks.${
            hasShortcut ? ' The shortcut has also been removed.' : ''
          }`,
        );
      } else {
        // Add bookmark
        await toggleBookmark();

        // Create shortcut if supported
        if (isShortcutSupported) {
          try {
            await createCompanyShortcut(id, name);
            await bookmarkStorageService.updateShortcutStatus(
              id,
              true,
              `company_${id}`,
            );
            await refreshBookmarkState();
            console.log(`Shortcut created for company: ${name} (ID: ${id})`);
          } catch (error) {
            console.error('Error creating company shortcut:', error);
            // Don't show error to user for shortcut creation failure
            // The bookmark action should still succeed
          }
        }

        Alert.alert(
          'Bookmarked',
          `You bookmarked ${name}.${
            isShortcutSupported
              ? ' A shortcut has been requested for your home screen.'
              : ''
          }`,
        );
      }
    } catch (error) {
      console.error('Error handling bookmark:', error);
      Alert.alert('Error', 'Failed to update bookmark. Please try again.');
    }
  };

  const handleOpenMap = (
    lat: string | number | null,
    lng: string | number | null,
  ) => {
    if (lat === null || lng === null) return;

    const latValue = typeof lat === 'string' ? parseFloat(lat) : lat;
    const lngValue = typeof lng === 'string' ? parseFloat(lng) : lng;

    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${latValue},${lngValue}`;
    Linking.openURL(mapUrl).catch(err => {
      console.error('Failed to open map:', err);
    });
  };

  const formatVoteCount = (count: number): string => {
    if (count < 1000) {
      return count.toString();
    }
    return (count / 1000).toFixed(1) + 'k';
  };

  const handlePress = () => {
    navigation.navigate('CompanyDetailsScreen', {
      title: companyData?.companyName,
      companyId: companyData?.companyId,
      fromHistory: companyData?.fromHistory || false,
    });
  };

  // Platform-specific touchable component is handled directly in the return statement

  const touchableProps =
    Platform.OS === 'android'
      ? {
          background: TouchableNativeFeedback.Ripple('#c0c0c0', false),
          useForeground: true,
        }
      : {
          activeOpacity: 0.7,
        };

  return (
    <TouchableOpacity onPress={handlePress} {...touchableProps}>
      <View style={styles.card}>
        <View style={styles.innerCard}>
          <View style={styles.cardContent}>
            <View style={styles.bankImageView}>
              <Image
                style={styles.categoriesIcon}
                source={imageSource}
                resizeMode="contain"
                defaultSource={Images.defaultIcon}
                onError={handleImageError}
              />
            </View>
            <View style={styles.bankInfoView}>
              <Text style={[styles.bankName]} numberOfLines={2}>
                {companyData?.companyName}
              </Text>
              {companyData?.number && (
                <View style={styles.phoneContainer}>
                  <Image
                    style={styles.phoneIcon}
                    source={Images.ic_phoneCall}
                  />
                  <TouchableOpacity
                    style={styles.phoneNumberTouchable}
                    activeOpacity={0.6}
                    onPress={() => {
                      console.log('Phone number touched:', companyData.number);
                      if (companyData.number) {
                        handlePhoneCall(companyData.number);
                      } else {
                        console.log('No phone number available');
                      }
                    }}>
                    <Text style={styles.phoneNumber}>{companyData.number}</Text>
                  </TouchableOpacity>
                </View>
              )}
              {/* Parent company 
              {companyData?.parentCompany && (
                <Text style={styles.parentCompany} numberOfLines={1}>
                  {companyData.parentCompany}
                </Text>
              )} */}
            </View>
          </View>
          <Image source={Images.ic_arrowRight} style={styles.rightArrowIcon} />
        </View>
        <View style={styles.cardSeperationView} />
        <View style={styles.cardFooter}>
          <View style={styles.generalInfo}>
            <TouchableOpacity
              onPress={handleUpVote}
              disabled={isVoting}
              style={[
                styles.iconContainer,
                isVoting && styles.disabledContainer,
              ]}>
              <Image style={styles.upVoteIcon} source={Images.ic_arrowUp} />
              <Text style={styles.upVoteText}>
                {formatVoteCount(localUpvoteCount)}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleDownVote}
              disabled={isVoting}
              style={[
                styles.iconContainer,
                isVoting && styles.disabledContainer,
              ]}>
              <Image style={styles.upVoteIcon} source={Images.ic_arrowDown} />
              <Text style={styles.upVoteText}>
                {formatVoteCount(localDownvoteCount)}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleBookmark}>
              <View
                style={[
                  styles.iconContainer,
                  isBookmarked && styles.bookmarkedIconContainer,
                ]}>
                <Image
                  style={[
                    styles.upVoteIcon,
                    isBookmarked && styles.bookmarkedIcon,
                  ]}
                  source={Images.ic_bookmarkSimple}
                />
              </View>
            </TouchableOpacity>
          </View>
          {companyData?.lat && companyData?.long && (
            <View style={styles.directionInfo}>
              <TouchableOpacity
                style={styles.directionsButton}
                onPress={() => {
                  if (companyData?.lat && companyData?.long) {
                    handleOpenMap(companyData.lat, companyData.long);
                  }
                }}>
                <Text style={styles.directionsText}>Direction</Text>
                <Image
                  style={styles.upVoteIcon}
                  source={Images.ic_navigationArrow}
                />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
    justifyContent: 'space-evenly',
  },
  innerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
  },
  bankImageView: {
    marginLeft: 10,
    marginTop: 2,
    marginBottom: 2,
    backgroundColor: '#D9E2EF',
    borderRadius: 6,
    overflow: 'hidden', // ensures anything overflowing gets clipped
  },
  phoneIcon: {
    marginLeft: 3,
    height: 18,
    width: 18,
    marginRight: 5,
  },
  bankInfoView: {
    flex: 1,
    paddingHorizontal: 'auto',
    marginLeft: 10,
    marginTop: 2,
    marginBottom: 2,
    flexDirection: 'column',
    maxWidth: '90%',
    overflow: 'hidden',
    justifyContent: 'flex-start', // Align items to the top
  },
  rightArrowIcon: {
    height: 26,
    width: 26,
    resizeMode: 'contain',
    marginRight: 10,
  },
  cardSeperationView: {
    height: 1,
    backgroundColor: '#D7E2F1',
    marginVertical: 2,
    width: '100%',
    flex: 1,
  },
  categoriesIcon: {
    height: 50,
    width: 50,
  },
  cardContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  bankName: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 20,
    marginTop: 3,
    marginBottom: 0, // Reduce space below bank name
  },
  phoneNumber: {
    fontSize: 17,
    fontFamily: 'Poppins-Medium',
    textDecorationLine: 'underline',
    color: '#0066cc',
    marginTop: -2, // Move up to align better with phone icon
    lineHeight: 20, // Ensure consistent line height for better alignment
  },
  parentCompany: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    paddingVertical: 2,
    justifyContent: 'space-between',
    height: 40,
  },
  generalInfo: {
    flexDirection: 'row',
    gap: 10,
    paddingBottom: 2,
    marginLeft: 10,
    alignItems: 'flex-start',
    width: 200,
  },
  directionInfo: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    width: 120,
    marginRight: 10,
  },
  iconContainer: {
    backgroundColor: '#D9E2EF',
    padding: 4,
    borderRadius: 6,
    height: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 2,
  },
  upVoteIcon: {
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  upVoteText: {
    marginTop: 1,
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    marginRight: 5,
  },
  directionsButton: {
    backgroundColor: '#D9E2EF',
    borderRadius: 6,
    flexDirection: 'row',
    padding: 4,
    height: 30,
  },
  directionsText: {
    marginTop: 1,
    marginLeft: 10,
    marginRight: 4,
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
  },
  phoneNumberTouchable: {
    paddingLeft: 2,
    paddingRight: 4,
    paddingVertical: 2,
    marginVertical: 0,
  },
  phoneContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: 22, // Reduced from 25 to make it more compact
    marginTop: -2, // Add negative margin to bring it closer to the bank name
    marginBottom: 3,
    // Ensure proper alignment between icon and text
    paddingVertical: 1,
  },
  bookmarkedIconContainer: {
    backgroundColor: '#4A90E2', // Blue background for bookmarked state
  },
  bookmarkedIcon: {
    tintColor: '#FFFFFF', // White tint for bookmarked icon
  },
  disabledContainer: {
    opacity: 0.6, // Reduce opacity when voting is in progress
  },
});

export default CustomCategoriesDetailCard;
