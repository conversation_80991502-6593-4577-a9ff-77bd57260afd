import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ScrollView} from 'react-native';
import voteConsistencyDebugger from '../utils/debugVoteConsistency';

const VoteDebugPanel = () => {
  const [debugOutput, setDebugOutput] = useState<string>('');
  const [isRunning, setIsRunning] = useState(false);

  const runDebug = async (debugFunction: () => Promise<void>, description: string) => {
    if (isRunning) return;
    
    setIsRunning(true);
    setDebugOutput(`Running ${description}...\n`);
    
    // Capture console.log output
    const originalLog = console.log;
    let output = '';
    
    console.log = (...args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      output += message + '\n';
      originalLog(...args);
    };

    try {
      await debugFunction();
      setDebugOutput(output);
    } catch (error) {
      setDebugOutput(output + `\nError: ${error}`);
    } finally {
      console.log = originalLog;
      setIsRunning(false);
    }
  };

  const debugHavells = () => runDebug(
    () => voteConsistencyDebugger.debugHavellsIndia(),
    'Havells India Debug'
  );

  const findInconsistencies = () => runDebug(
    () => voteConsistencyDebugger.findInconsistentCompanies(20),
    'Finding Vote Inconsistencies'
  );

  const debugSpecificCompany = () => runDebug(
    () => voteConsistencyDebugger.debugCompanyVotes(33542), // Havells companyId from your example
    'Specific Company Debug'
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Vote Consistency Debugger</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isRunning && styles.buttonDisabled]} 
          onPress={debugHavells}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>Debug Havells India</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isRunning && styles.buttonDisabled]} 
          onPress={findInconsistencies}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>Find Inconsistencies</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isRunning && styles.buttonDisabled]} 
          onPress={debugSpecificCompany}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>Debug Company 33542</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.clearButton]} 
          onPress={() => setDebugOutput('')}
        >
          <Text style={styles.buttonText}>Clear Output</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.outputContainer}>
        <Text style={styles.output}>{debugOutput || 'No debug output yet. Tap a button to start debugging.'}</Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  outputContainer: {
    flex: 1,
    backgroundColor: '#000',
    borderRadius: 8,
    padding: 10,
  },
  output: {
    color: '#00FF00',
    fontFamily: 'Courier',
    fontSize: 12,
  },
});

export default VoteDebugPanel;
