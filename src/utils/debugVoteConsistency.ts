/**
 * Debug utility to check vote count consistency between company list and details
 */

import numberRepository from '../database/watermelon/repositories/numberRepository';
import companyRepository from '../database/watermelon/repositories/companyRepository';
import { extractFirstAvailableNumber } from './numberUtils';

export class VoteConsistencyDebugger {
  
  /**
   * Debug vote counts for a specific company
   */
  async debugCompanyVotes(companyId: number) {
    console.log(`\n🔍 === VOTE CONSISTENCY DEBUG FOR COMPANY ${companyId} ===`);

    try {
      // 1. Get company data from database
      const company = await companyRepository.getByCompanyId(companyId);
      if (!company) {
        console.log(`❌ Company ${companyId} not found in database`);
        return;
      }

      console.log(`\n📊 Company: ${company.company_name} (ID: ${companyId})`);
      console.log(`   Company-level votes: ${company.upvote_count}↑ ${company.downvote_count}↓`);
      console.log(`   First available number: "${company.number}"`);

      // 2. Get all numbers for this company
      const numbers = await numberRepository.getByCompanyId(companyId);
      console.log(`\n📞 Found ${numbers.length} phone numbers:`);
      
      numbers.forEach((number, index) => {
        const isFirstAvailable = number.number === company.number;
        console.log(`   ${index + 1}. ${number.number} (${number.type}) - ${number.upvote_count}↑ ${number.downvote_count}↓ ${isFirstAvailable ? '← FIRST AVAILABLE' : ''}`);
      });

      // 3. Check which number would be shown in the list
      const firstAvailableNumber = company.number;
      const matchingNumber = numbers.find(num => num.number === firstAvailableNumber);
      
      console.log(`\n🎯 Vote Count Analysis:`);
      console.log(`   List shows (first available): "${firstAvailableNumber}"`);
      if (matchingNumber) {
        console.log(`   List vote counts: ${matchingNumber.upvote_count}↑ ${matchingNumber.downvote_count}↓`);
      } else {
        console.log(`   ❌ First available number not found in numbers table!`);
        console.log(`   Fallback to company-level: ${company.upvote_count}↑ ${company.downvote_count}↓`);
      }

      console.log(`\n   Details page shows all numbers individually:`);
      numbers.forEach((number, index) => {
        console.log(`     ${index + 1}. ${number.number}: ${number.upvote_count}↑ ${number.downvote_count}↓`);
      });

      // 4. Check for inconsistencies
      const hasInconsistency = numbers.some(num => 
        num.upvote_count !== (matchingNumber?.upvote_count || company.upvote_count) ||
        num.downvote_count !== (matchingNumber?.downvote_count || company.downvote_count)
      );

      if (hasInconsistency) {
        console.log(`\n⚠️  INCONSISTENCY DETECTED!`);
        console.log(`   The vote counts shown in the list vs details page will be different.`);
      } else {
        console.log(`\n✅ Vote counts are consistent.`);
      }

    } catch (error) {
      console.error('Error debugging company votes:', error);
    }
  }

  /**
   * Find companies with vote count inconsistencies
   */
  async findInconsistentCompanies(limit: number = 10) {
    console.log(`\n🔍 === FINDING COMPANIES WITH VOTE INCONSISTENCIES ===`);

    try {
      const companies = await companyRepository.getAll();
      console.log(`Checking ${Math.min(limit, companies.length)} companies...`);

      const inconsistentCompanies = [];

      for (let i = 0; i < Math.min(limit, companies.length); i++) {
        const company = companies[i];
        const numbers = await numberRepository.getByCompanyId(company.company_id!);
        
        if (numbers.length === 0) continue;

        // Find the first available number
        const firstAvailableNumber = company.number;
        const matchingNumber = numbers.find(num => num.number === firstAvailableNumber);
        
        // Check if there are different vote counts among numbers
        const voteCountsSet = new Set(numbers.map(num => `${num.upvote_count}-${num.downvote_count}`));
        
        if (voteCountsSet.size > 1) {
          inconsistentCompanies.push({
            companyId: company.company_id,
            companyName: company.company_name,
            firstAvailableNumber,
            firstAvailableVotes: matchingNumber ? `${matchingNumber.upvote_count}↑ ${matchingNumber.downvote_count}↓` : 'Not found',
            allNumbers: numbers.map(num => ({
              number: num.number,
              type: num.type,
              votes: `${num.upvote_count}↑ ${num.downvote_count}↓`
            }))
          });
        }
      }

      console.log(`\n📊 Found ${inconsistentCompanies.length} companies with vote inconsistencies:`);
      inconsistentCompanies.forEach((company, index) => {
        console.log(`\n${index + 1}. ${company.companyName} (ID: ${company.companyId})`);
        console.log(`   List shows: ${company.firstAvailableNumber} - ${company.firstAvailableVotes}`);
        console.log(`   All numbers:`);
        company.allNumbers.forEach(num => {
          console.log(`     ${num.number} (${num.type}): ${num.votes}`);
        });
      });

      return inconsistentCompanies;

    } catch (error) {
      console.error('Error finding inconsistent companies:', error);
      return [];
    }
  }

  /**
   * Quick check for Havells India specifically
   */
  async debugHavellsIndia() {
    console.log(`\n🔍 === DEBUGGING HAVELLS INDIA SPECIFICALLY ===`);

    try {
      const companies = await companyRepository.getAll();
      const havells = companies.find(company => 
        company.company_name.toLowerCase().includes('havells')
      );

      if (havells) {
        console.log(`Found Havells: ${havells.company_name} (ID: ${havells.company_id})`);
        await this.debugCompanyVotes(havells.company_id!);
      } else {
        console.log(`❌ Havells India not found in database`);
      }

    } catch (error) {
      console.error('Error debugging Havells India:', error);
    }
  }
}

export default new VoteConsistencyDebugger();
