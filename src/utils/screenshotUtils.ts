/**
 * Screenshot utility for capturing and sharing screen content
 */

import { Alert, Share, Platform } from 'react-native';
import { captureRef } from 'react-native-view-shot';

export interface ScreenshotOptions {
  format?: 'png' | 'jpg' | 'webm';
  quality?: number; // 0.0 to 1.0
  result?: 'tmpfile' | 'base64' | 'zip-base64' | 'data-uri';
  snapshotContentContainer?: boolean;
}

export class ScreenshotService {
  
  /**
   * Capture screenshot of a specific view reference
   * @param viewRef - React ref to the view to capture
   * @param options - Screenshot options
   * @returns Promise with the screenshot URI or base64 data
   */
  static async captureView(
    viewRef: React.RefObject<any>,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    if (!viewRef.current) {
      throw new Error('View reference is not available');
    }

    const defaultOptions: ScreenshotOptions = {
      format: 'png',
      quality: 0.8,
      result: 'tmpfile',
      snapshotContentContainer: false,
      ...options,
    };

    try {
      console.log('[ScreenshotService] Capturing screenshot...');
      const uri = await captureRef(viewRef, defaultOptions);
      console.log('[ScreenshotService] Screenshot captured successfully:', uri);
      return uri;
    } catch (error) {
      console.error('[ScreenshotService] Error capturing screenshot:', error);
      throw new Error('Failed to capture screenshot');
    }
  }

  /**
   * Capture and share screenshot of a view
   * @param viewRef - React ref to the view to capture
   * @param shareOptions - Additional sharing options
   * @param screenshotOptions - Screenshot capture options
   */
  static async captureAndShare(
    viewRef: React.RefObject<any>,
    shareOptions: {
      title?: string;
      message?: string;
      subject?: string;
    } = {},
    screenshotOptions: ScreenshotOptions = {}
  ): Promise<void> {
    try {
      // Show loading state could be handled by caller
      console.log('[ScreenshotService] Starting capture and share process...');

      // Capture the screenshot
      const screenshotUri = await this.captureView(viewRef, screenshotOptions);

      // Prepare share content
      const shareContent: any = {
        title: shareOptions.title || 'India Customer Care',
        message: shareOptions.message || 'Check out this company information from India Customer Care app!',
        url: screenshotUri,
      };

      // Add subject for email sharing (iOS)
      if (Platform.OS === 'ios' && shareOptions.subject) {
        shareContent.subject = shareOptions.subject;
      }

      console.log('[ScreenshotService] Sharing screenshot...');
      
      // Share the screenshot
      const result = await Share.share(shareContent);

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('[ScreenshotService] Shared with activity type:', result.activityType);
        } else {
          console.log('[ScreenshotService] Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('[ScreenshotService] Share dismissed by user');
      }

    } catch (error) {
      console.error('[ScreenshotService] Error in capture and share:', error);
      
      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (errorMessage.includes('Failed to capture screenshot')) {
        Alert.alert(
          'Screenshot Error',
          'Unable to capture the screen. Please try again.',
          [{ text: 'OK' }]
        );
      } else if (errorMessage.includes('View reference is not available')) {
        Alert.alert(
          'Error',
          'Screen is not ready for capture. Please wait a moment and try again.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Share Error',
          'Unable to share the screenshot. Please try again.',
          [{ text: 'OK' }]
        );
      }
      
      throw error; // Re-throw for caller to handle if needed
    }
  }

  /**
   * Capture screenshot with retry logic
   * @param viewRef - React ref to the view to capture
   * @param maxRetries - Maximum number of retry attempts
   * @param retryDelay - Delay between retries in milliseconds
   * @param options - Screenshot options
   */
  static async captureWithRetry(
    viewRef: React.RefObject<any>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[ScreenshotService] Capture attempt ${attempt}/${maxRetries}`);
        return await this.captureView(viewRef, options);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(`[ScreenshotService] Attempt ${attempt} failed:`, lastError.message);
        
        if (attempt < maxRetries) {
          console.log(`[ScreenshotService] Retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    throw lastError || new Error('Screenshot capture failed after all retries');
  }

  /**
   * Get optimal screenshot options based on device and use case
   * @param useCase - The intended use case for the screenshot
   */
  static getOptimalOptions(useCase: 'share' | 'save' | 'preview' = 'share'): ScreenshotOptions {
    const baseOptions: ScreenshotOptions = {
      format: 'png',
      snapshotContentContainer: false,
    };

    switch (useCase) {
      case 'share':
        return {
          ...baseOptions,
          quality: 0.8, // Good quality for sharing
          result: 'tmpfile', // File for sharing
        };
      
      case 'save':
        return {
          ...baseOptions,
          quality: 0.9, // Higher quality for saving
          result: 'tmpfile',
        };
      
      case 'preview':
        return {
          ...baseOptions,
          quality: 0.6, // Lower quality for quick preview
          result: 'base64',
        };
      
      default:
        return baseOptions;
    }
  }
}

export default ScreenshotService;
