{"name": "IndiaCustomerCare", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start"}, "dependencies": {"@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-voice/voice": "npm:yet-another-react-native-voice@4.0.0", "@react-native/gradle-plugin": "^0.79.2", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@reduxjs/toolkit": "^2.7.0", "@types/react-native": "^0.73.0", "axios": "^1.9.0", "react": "19.0.0", "react-native": "^0.79.1", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-splash-screen": "^3.3.0", "react-native-toast-message": "^2.3.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.0", "@react-native/eslint-config": "0.79.0", "@react-native/metro-config": "0.79.0", "@react-native/typescript-config": "0.79.0", "@types/lodash": "^4.17.17", "@types/react": "^19.0.0", "eslint": "^8.19.0", "prettier": "2.8.8", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}